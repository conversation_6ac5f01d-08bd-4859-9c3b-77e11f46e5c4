import { formatSlug, formatSlugHook } from "@/utils/format-slug";
import type { CheckboxField, RelationshipField, TextField } from "payload";

type Overrides = {
	slugOverrides?: Partial<TextField>;
	checkboxOverrides?: Partial<CheckboxField>;
};

type Slug = (
	fieldToUse?: string,
	overrides?: Overrides,
) => [TextField, CheckboxField];

export const slugField: Slug = (fieldToUse = "title", overrides = {}) => {
	const { slugOverrides, checkboxOverrides } = overrides;

	const checkBoxField: CheckboxField = {
		name: "slugLock",
		type: "checkbox",
		defaultValue: true,
		admin: {
			hidden: true,
			position: "sidebar",
		},
		...checkboxOverrides,
	};

	const parentPageField: RelationshipField = {
		name: "parent",
		label: "Parent Page",
		type: "relationship",
		relationTo: "pages", // adjust to your actual collection slug
		admin: {
			position: "sidebar",
		},
	};

	// Expect ts error here because of typescript mismatching Partial<TextField> with TextField
	// @ts-expect-error
	const slugField: TextField = {
		name: "slug",
		type: "text",
		index: true,
		label: "Slug",
		...(slugOverrides || {}),
		hooks: {
			beforeValidate: [
				async ({ value, siblingData, req: { payload } }) => {
					const title = siblingData?.[fieldToUse];
					const parent = siblingData?.parent;

					let parentSlug = "";

					if (parent && typeof parent === "object") {
						parentSlug = parent.slug || ""; // If populated
					} else if (parent) {
						const parentDoc = await payload.findByID({
							collection: "pages",
							id: parent,
						});
						parentSlug = parentDoc?.slug || "";
					}

					const currentSlug = formatSlug(title || value || "");
					return parentSlug ? `${parentSlug}/${currentSlug}` : currentSlug;
				},
			],
		},
		admin: {
			position: "sidebar",
			description:
				"Optional: Wählen Sie eine übergeordnete Seite, unter der diese Seite erscheinen soll. Zum Beispiel könnte 'Team' eine Unterseite von 'Über uns' sein.",
			...(slugOverrides?.admin || {}),
			components: {
				Field: {
					path: "@/fields/slug.component.tsx#SlugComponent",
					clientProps: {
						fieldToUse,
						checkboxFieldPath: checkBoxField.name,
					},
				},
			},
		},
	};

	return [slugField, parentPageField, checkBoxField];
};
