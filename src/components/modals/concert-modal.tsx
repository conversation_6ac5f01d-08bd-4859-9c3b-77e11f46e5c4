"use client";

import * as Dialog from "@radix-ui/react-dialog";
import { X } from "lucide-react";
import Lenis from "lenis";
import { useEffect, useRef } from "react";
import { useModal } from "@/hooks/use-modal";
import { Media } from "../render/render-media";

export const ConcertModal = () => {
	const {
		isModalOpen,
		setIsOpen,
		data: { concert },
	} = useModal();

	const wrapperRef = useRef<HTMLDivElement | null>(null);
	const lenisRef = useRef<Lenis | null>(null);

	useEffect(() => {
		if (isModalOpen) {
			document.body.style.overflow = "hidden"; // Lock background scroll

			// Wait for the modal DOM to be present
			const timeout = setTimeout(() => {
				if (!wrapperRef.current) return;

				const content = wrapperRef.current.querySelector(
					"[data-lenis-content]",
				) as HTMLElement;
				if (!content) return;

				const lenis = new Lenis({
					wrapper: wrapperRef.current as HTMLElement,
					content: content,
					duration: 1.2,
					gestureOrientation: "vertical",
				});

				lenisRef.current = lenis;

				const raf = (time: number) => {
					lenis.raf(time);
					requestAnimationFrame(raf);
				};
				requestAnimationFrame(raf);
			}, 0); // defer to next tick

			return () => {
				clearTimeout(timeout);
				document.body.style.overflow = "";
				lenisRef.current?.destroy();
				lenisRef.current = null;
			};
		}

		document.body.style.overflow = "";
		lenisRef.current?.destroy();
		lenisRef.current = null;
	}, [isModalOpen]);

	const handleClose = () => {
		setIsOpen(false, { concert: concert });

		setTimeout(() => {
			if (concert) {
				setIsOpen(false, { concert: null });
			}
		}, 500);
	};

	return (
		<Dialog.Root open={isModalOpen} onOpenChange={handleClose}>
			<Dialog.Portal>
				<Dialog.Overlay className="fixed inset-0 bg-secondary/40 z-40 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0" />
				<Dialog.Content className="DialogContent | fixed right-0 top-0 z-50 h-full w-full max-w-[50vw] bg-primary overflow-hidden shadow-xl data-[state=open]:animate-modal-in data-[state=closed]:animate-modal-out">
					<div
						ref={wrapperRef}
						data-lenis-wrapper
						className="h-full w-full overflow-hidden will-change-transform"
					>
						<div
							data-lenis-content
							className="min-h-full will-change-transform"
						>
							<Dialog.Close asChild>
								<button
									type="button"
									className="fixed top-6 right-6 cursor-pointer z-10"
								>
									<div className="size-40">
										<div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 size-32">
											<span className="h-px w-full bg-secondary rotate-45 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2" />
											<span className="h-px w-full bg-secondary -rotate-45 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2" />
										</div>
										<p className="h-full w-full bg-contrast rounded-full grid place-items-center text-sm text-center">
											De Gaudi
											<br /> wieder <br />
											zuamochn
										</p>
									</div>
								</button>
							</Dialog.Close>

							{concert && (
								<div className="">
									<div className="relative">
										<Media
											resource={concert.image}
											className="aspect-video mb-8"
											imgClassName="object-cover h-full w-full"
										/>
									</div>
									<div className="px-6">
										<div className="mb-8">
											<Dialog.Title asChild>
												<h3>
													<strong>{concert.title}</strong>
												</h3>
											</Dialog.Title>
											<Dialog.Description asChild>
												<p className="h3">{concert.subline}</p>
											</Dialog.Description>
										</div>
										<div className="grid [grid-template-columns:max-content_1fr] gap-x-4 gap-y-2">
											<p>
												<strong>WANN:</strong>
											</p>
											<p>{concert.formattedDateString}</p>
											<p>
												<strong>WO:</strong>
											</p>
											<p>{concert.where}</p>
										</div>
									</div>
								</div>
							)}
						</div>
					</div>
				</Dialog.Content>
			</Dialog.Portal>
		</Dialog.Root>
	);
};
