"use client";

import { useRef, type FC } from "react";
import Link from "next/link";
import type { <PERSON>, Header, Page } from "@/payload-types";
import { CMSLink } from "../cms-link";
import { MegaNav } from "./mega-nav";
import { useModal } from "@/hooks/use-modal";

interface INavigationProps {
	header: Header;
	nextConcert: Concert;
}

export const Navigation: FC<INavigationProps> = ({ header, nextConcert }) => {
	const navRef = useRef<HTMLDivElement>(null);
	const { setIsOpen } = useModal();

	return (
		<header className="fixed inset-x-0 top-0 z-50 layout-block">
			<div className="default-grid py-safe items-start" ref={navRef}>
				<div className="col-span-4 relative pl-12">
					<CMSLink
						type="reference"
						reference={{ relationTo: "pages", value: { slug: "home" } as Page }}
						className="cursor-pointer"
					>
						<span className="font-bold text-3xl">Hard-Chor</span>
					</CMSLink>
					<img
						src="/logo.svg"
						alt="logo"
						className="pointer-events-none absolute top-0 left-0 w-auto h-full scale-200"
					/>
				</div>
				<nav className="col-span-8 col-start-5 flex justify-between">
					<ul className="flex items-center gap-x-4">
						{header.navItems?.map((item) => {
							if (item.isMegaNav) {
								return (
									<li key={item.id} className="text-sm relative">
										<MegaNav item={item} navRef={navRef}>
											{item.label}
										</MegaNav>
									</li>
								);
							}

							return (
								<li key={item.id} className="text-sm">
									<CMSLink {...item.link} appearance="underline" />
								</li>
							);
						})}
					</ul>
					<button
						type="button"
						className="relative w-[160px] overflow-hidden text-xs px-2 bg-secondary text-contrast font-bold uppercase cursor-pointer hover:bg-contrast hover:text-secondary transition-colors ease-out-expo duration-500"
						onClick={() => {
							setIsOpen(true, { concert: nextConcert });
						}}
					>
						<div className="animate-marquee whitespace-nowrap">
							Nächstes Konzert - Nächstes Konzert - Nächstes Konzert -
						</div>
					</button>
				</nav>
			</div>
		</header>
	);
};
